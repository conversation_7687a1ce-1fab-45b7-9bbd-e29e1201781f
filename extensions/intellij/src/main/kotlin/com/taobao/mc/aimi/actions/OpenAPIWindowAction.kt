package com.taobao.mc.aimi.actions

import com.github.continuedev.continueintellijextension.actions.getContinuePluginService
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.taobao.mc.aimi.types.WindowType

/**
 * 打开API窗口的Action
 */
class OpenAPIWindowAction : AnAction("API") {
    override fun actionPerformed(e: AnActionEvent) {
        val continuePluginService = getContinuePluginService(e.project) ?: return
        continuePluginService.openNonResidentWindow(WindowType.API)
    }
}
