package com.taobao.mc.aimi.services.search

import com.intellij.ide.actions.searcheverywhere.*
import com.intellij.ide.util.gotoByName.GotoClassSymbolConfiguration
import com.intellij.ide.util.gotoByName.LanguageRef
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.Service
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.intellij.util.application
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.SymbolInfo
import com.taobao.mc.aimi.util.Reflect
import com.taobao.mc.aimi.util.editor
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.concurrent.Executor
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

@Service(Service.Level.PROJECT)
class SymbolSearchService : DumbAware {
    private val logger = LoggerManager.getLogger(javaClass)

    suspend fun searchSymbols(project: Project, searchPattern: String, anActionEvent: AnActionEvent): List<SymbolInfo> {
        searchTypeDeclaration(project, searchPattern)
        return suspendCancellableCoroutine { continuation ->
            // 1. 创建搜索贡献者列表 - 只包含 Symbol 贡献者
            val contributors = listOf<SearchEverywhereContributor<*>>(
                object : SymbolSearchEverywhereContributor(anActionEvent) {
                    init {
                        val ids = listOf("kotlin", "knd", "java")
                        val items = LanguageRef.forAllLanguages().filter { ids.contains(it.id.lowercase()) }
                        val persistentConfig = GotoClassSymbolConfiguration.getInstance(project)
                        val filter = PersistentSearchEverywhereContributorFilter(
                            items,
                            persistentConfig,
                            LanguageRef::displayName,
                            LanguageRef::icon
                        )
                        Reflect.on(this)
                            .set("myFilter", filter)
                    }

                }.apply {
                    val myFilter = Reflect.on(this).get<Any>("myFilter")
                    logger.info("myScopeDescriptor: $myFilter")
                }
            )
            val contributorsMap = HashMap<SearchEverywhereContributor<*>?, Int?>()
            contributors.forEach {
                contributorsMap[it] = 5
            }

            var progressIndicator: ProgressIndicator? = null
            val items = mutableListOf<SymbolInfo>()
            var searchError: Throwable? = null

            // 2. 创建搜索监听器
            val searchListener: SearchListener = object : SearchListener {
                private var startTime = 0L

                override fun elementsAdded(list: MutableList<out SearchEverywhereFoundElementInfo>) {
                    // 处理搜索结果
                    for (info in list) {
                        runCatching {
                            val element = info.getElement()
                            val elementProxy = Reflect.on(element)
                            val content = elementProxy
                                .call("getText")
                                .get<Any>()
                                .toString()
                            val containingFile = elementProxy.call("getContainingFile")
                            val pkgName = containingFile.call("getPackageName").get<Any>().toString()
                            val virtualFile = containingFile.call("getVirtualFile").get<VirtualFile?>()
                            logger.info("Found symbol: $element, pkg: $pkgName, file: ${virtualFile?.url}, content: $content")
                            items.add(
                                SymbolInfo(
                                    filepath = virtualFile?.url ?: "",
                                    content = content
                                )
                            )
                        }.onFailure { error ->
                            logger.warn("Error processing search result", error)
                            if (searchError == null) {
                                searchError = error
                            }
                        }
                    }
                }

                override fun elementsRemoved(list: MutableList<out SearchEverywhereFoundElementInfo?>) {
                    // 处理移除的元素
                }

                override fun contributorWaits(contributor: SearchEverywhereContributor<*>) {
                    // 贡献者等待状态
                }

                override fun contributorFinished(
                    contributor: SearchEverywhereContributor<*>,
                    hasMore: Boolean
                ) {
                    // 贡献者完成搜索
                    logger.info("Contributor finished: ${contributor.searchProviderId}, hasMore: $hasMore")
                }

                override fun searchFinished(hasMoreMap: MutableMap<SearchEverywhereContributor<*>?, Boolean?>) {
                    // 搜索完成 - 恢复协程
                    val searchTime = System.currentTimeMillis() - startTime
                    logger.info("Search finished, cost: ${searchTime}ms, hasMore: $hasMoreMap, results: ${items.size}")

                    // 取消进度指示器
                    progressIndicator?.cancel()

                    if (continuation.isActive) {
                        if (searchError != null && items.isEmpty()) {
                            continuation.resumeWithException(searchError)
                        } else {
                            continuation.resume(items.toList())
                        }
                    }
                }

                override fun searchStarted(pattern: String, contributors: MutableCollection<out SearchEverywhereContributor<*>?>) {
                    logger.info("Search started: $pattern, contributors: $contributors")
                    startTime = System.currentTimeMillis()
                }
            }

            try {
                // 3. 创建 GroupedResultsSearcher, 用反射实现
                val onClass = Reflect.onClass("com.intellij.ide.actions.searcheverywhere.GroupedResultsSearcher")
                val instance = onClass.create(
                    searchListener,
                    object : Executor {
                        override fun execute(command: Runnable) {
                            // 可以使用 ApplicationManager.getApplication().executeOnPooledThread(command)
                            // 或者直接在当前线程执行
                            application.invokeLater(command)
                        }
                    },
                    SEResultsEqualityProvider.providers
                )

                // 4. 开始搜索
                val queryStr = searchPattern.substringAfterLast(".").substringBeforeLast("(").trim()
                progressIndicator = instance.call("search", contributorsMap, queryStr).get()

                // 设置协程取消回调
                continuation.invokeOnCancellation {
                    logger.info("Search cancelled by coroutine")
                    progressIndicator?.cancel()
                }

            } catch (error: Throwable) {
                logger.error("Error starting search", error)
                if (continuation.isActive) {
                    continuation.resumeWithException(error)
                }
            }
        }
    }

    suspend fun searchTypeDeclaration(project: Project, searchPattern: String) {
        val editor = FileEditorManager.getInstance(project).selectedEditor?.editor ?: return
        val document = editor.document
        val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document) ?: return
        // 获取光标位置
        val offset = editor.caretModel.offset
        // 获取光标位置的PSI元素
        val elementAtCursor = psiFile.findElementAt(offset) ?: return

        val curText = elementAtCursor.text
        val line = editor.caretModel.logicalPosition.line
        val column = editor.caretModel.logicalPosition.column
        val lineStart = editor.document.getLineStartOffset(line)
        val lineEnd = editor.document.getLineEndOffset(line)
        val text = editor.document.getText(com.intellij.openapi.util.TextRange(lineStart, lineEnd))
        
        // 分析调用链中的对象位置
        val callChainPositions = analyzeCallChainPositions(text, curText, line, column)
        
        // 输出调用链分析结果
        logger.info("Call chain analysis for curText: '$curText'")
        callChainPositions.forEach { position ->
            logger.info("Object: '${position.objectText}' at line ${position.line}, column ${position.startColumn}-${position.endColumn}")
        }
    }

    /**
     * 分析调用链中对象的文本位置
     * @param lineText 当前行的完整文本
     * @param curText 光标处的文本
     * @param lineNumber 当前行号
     * @param cursorColumn 光标列号
     * @return 调用链中各对象的位置信息列表
     */
    private fun analyzeCallChainPositions(
        lineText: String, 
        curText: String, 
        lineNumber: Int, 
        cursorColumn: Int
    ): List<CallChainPosition> {
        val positions = mutableListOf<CallChainPosition>()
        
        // 查找当前光标所在的调用链
        val callChainRegex = Regex("""[a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*(?:\([^)]*\))?""")
        val matches = callChainRegex.findAll(lineText)
        
        // 找到包含光标位置的调用链
        var targetCallChain: MatchResult? = null
        for (match in matches) {
            if (cursorColumn >= match.range.first && cursorColumn <= match.range.last) {
                targetCallChain = match
                break
            }
        }
        
        if (targetCallChain != null) {
            val callChainText = targetCallChain.value
            val callChainStartColumn = targetCallChain.range.first
            
            // 解析调用链中的各个部分
            parseCallChainObjects(callChainText, lineNumber, callChainStartColumn, positions)
        } else {
            // 如果没有找到完整的调用链，至少分析当前文本
            val curTextStartColumn = lineText.indexOf(curText)
            if (curTextStartColumn != -1) {
                positions.add(
                    CallChainPosition(
                        objectText = curText,
                        line = lineNumber,
                        startColumn = curTextStartColumn,
                        endColumn = curTextStartColumn + curText.length - 1
                    )
                )
            }
        }
        
        return positions
    }

    /**
     * 解析调用链中的各个对象
     * @param callChainText 调用链文本
     * @param lineNumber 行号
     * @param startColumn 调用链在行中的起始列
     * @param positions 用于存储位置信息的列表
     */
    private fun parseCallChainObjects(
        callChainText: String, 
        lineNumber: Int, 
        startColumn: Int, 
        positions: MutableList<CallChainPosition>
    ) {
        // 使用正则表达式匹配对象名称和方法调用
        val objectPattern = Regex("""([a-zA-Z_][a-zA-Z0-9_]*)(?:\([^)]*\))?""")
        val matches = objectPattern.findAll(callChainText)
        
        for (match in matches) {
            val objectText = match.groupValues[1] // 只取对象/方法名，不包括括号
            val objectStartInChain = match.range.first
            val objectEndInChain = match.range.first + objectText.length - 1
            
            positions.add(
                CallChainPosition(
                    objectText = objectText,
                    line = lineNumber,
                    startColumn = startColumn + objectStartInChain,
                    endColumn = startColumn + objectEndInChain
                )
            )
        }
    }

    /**
     * 调用链位置信息数据类
     */
    data class CallChainPosition(
        val objectText: String,      // 对象/方法名称
        val line: Int,               // 行号
        val startColumn: Int,        // 起始列号
        val endColumn: Int           // 结束列号
    )
}