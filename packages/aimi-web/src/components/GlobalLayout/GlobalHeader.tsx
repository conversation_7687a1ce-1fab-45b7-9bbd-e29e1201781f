import React from 'react';
import {
  Avatar,
  Dropdown,
  Flex,
  Layout,
  MenuProps,
  Popover,
  theme,
} from 'antd';
import { useNavigate } from 'ice';
import type { User } from '@ali/mc-services';
import { UserOutlined } from '@ant-design/icons';
import styles from './index.module.less';
import Logo from './Logo';
import HeaderTab from './HeaderTab';
import Cookie from 'js-cookie';
import { Copy } from '@ali/mc-uikit';
import { CopyOutlined } from '@ali/mc-icons';
import { queryAgentToken } from '@ali/mc-services/AiMiAgent';
import { useRequest } from '@ali/mc-request';
import { AgentTokenVO } from '@ali/mc-services/AiMiDataContracts';

const { Header } = Layout;
export interface GlobalHerderProps {
  /**
   * 位于右侧的第一个扩展插槽，处于头部靠右展示部分的第一个位置，一般位于全局搜索的左边
   */
  addon?: React.ReactNode;

  /**
   * 位于头像左侧的扩展插槽
   */
  userAddonBefore?: React.ReactNode;

  /**
   * 当前登陆用户
   */
  user?: User;

  /**
   * 位于头像右侧的扩展插槽
   */
  userAddonAfter?: React.ReactNode;

  userMenu?: {
    items: MenuProps['items'];
    onClick: MenuProps['onClick'];
  };

}

const TabHeight = 56;

export default function GlobalHeader(props: GlobalHerderProps) {
  const {
    addon,
    userAddonBefore,
    user,
    userAddonAfter,
    userMenu,
  } = props;
  const { token } = theme.useToken();
  const navigate = useNavigate();

  const { runAsync: requestAgentToken } = useRequest<AgentTokenVO, []>(queryAgentToken);
  const headerHeight = 24 + TabHeight;

  const [agentToken, setAgentToken] = React.useState<string>();

  const requestAndSetTickets = async () => {
    const res = await requestAgentToken();
    setAgentToken(res?.token);
    const inFourMinutes = new Date(new Date().getTime() + 4 * 60 * 1000);
    res?.token && Cookie.set('agentToken', res?.token, { expires: inFourMinutes });
  };


  return (
    <Header
      className={styles.globalHeader}
      style={{
        height: `${headerHeight}px`,
        position: 'sticky',
        top: 0,
        zIndex: 1,
        background: 'linear-gradient(180deg, #0A0A0A 0%, rgba(10, 10, 10, 0) 100%)',
      }}
    >
      <Flex
        vertical
        style={{
          height: TabHeight,
          maxWidth: 1150,
          minWidth: 650,
          width: 'calc(100vw - 48px)',
          margin: '0 auto',
          paddingInline: token.padding * 2,
          border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,
          borderRadius: token.borderRadiusLG * 2,
          marginTop: token.marginLG,
          backgroundColor: 'rgb(1, 4, 9, 0.7)',
          backdropFilter: 'blur(5px)',
        }}
      >
        <Flex
          align="center"
          justify="center"
          flex={1}
          style={{
            position: 'relative',
          }}
          wrap={false}
        >
          <Flex
            flex={'0 0 auto'}
            gap={16}
            style={{
              position: 'absolute',
              left: 0,
            }}
          >
            <Flex gap="middle" align="center" className={styles.logo} onClick={() => navigate('/')}>
              <Logo />
            </Flex>
          </Flex>
          <Flex
            flex={1}
            justify="center"
          >
            <HeaderTab />
          </Flex>
          <Flex
            flex={1}
            gap={token.marginXS}
            style={{
              height: token.Layout?.headerHeight,
              position: 'absolute',
              right: 0,
            }}
            align="center"
            justify="flex-end"
          >
            {addon && <div className={styles.addon}>{addon}</div>}
            {userAddonBefore && <div className={styles.addon}>{userAddonBefore}</div>}
            <Dropdown
              menu={{
                items: userMenu?.items || [],
                onClick: userMenu?.onClick,
              }}
            >
              <Flex flex={'0 0 auto'}>
                <Popover
                  title="token"
                  trigger={['click', 'hover']}
                  onOpenChange={(open) => {
                     if (open) {
                      if (Cookie.get('agentToken')) {
                        setAgentToken(Cookie.get('agentToken'));
                      } else {
                        requestAndSetTickets();
                      }
                    }
                  }}
                  content={
                    agentToken && <Flex
                      style={{
                        border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,
                        borderRadius: token.borderRadius,
                        padding: token.paddingSM,
                        paddingInlineEnd: token.paddingSM * 3,
                        position: 'relative',
                      }}
                    >
                      {agentToken}
                      <Copy
                        text={agentToken ?? ''}
                        styles={{
                          position: 'absolute',
                          right: token.paddingSM,
                        }}
                      >
                        <CopyOutlined />
                      </Copy>
                    </Flex>
                  }
                >
                  <Avatar
                    icon={<UserOutlined />}
                    src={user ? <img src={user?.avatar} /> : null}
                    alt="avatar"
                    size={32}
                    style={{
                      cursor: 'pointer',
                    }}
                  />
                </Popover>
              </Flex>
            </Dropdown>
            {userAddonAfter && <Flex align="center" className={styles.addon}>{userAddonAfter}</Flex>}
          </Flex>

        </Flex>
      </Flex>
    </Header>
  );
}
