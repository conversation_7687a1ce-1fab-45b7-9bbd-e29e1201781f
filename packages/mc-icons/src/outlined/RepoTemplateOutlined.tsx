import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function RepoTemplateOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-repo-template-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_03925">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_03925)">
          <path
            d="M6,0.75C6,0.335786,6.33579,0,6.75,0L9.25,0C9.66421,0,10,0.335786,10,0.75C10,1.16421,9.66421,1.5,9.25,1.5L6.75,1.5C6.33579,1.5,6,1.16421,6,0.75ZM11,0.75C11,0.335786,11.3358,0,11.75,0L13.25,0C13.6642,0,14,0.335786,14,0.75L14,2.25C14,2.66421,13.6642,3,13.25,3C12.8358,3,12.5,2.66421,12.5,2.25L12.5,1.5L11.75,1.5C11.3358,1.5,11,1.16421,11,0.75ZM4.99244,0.661524C5.05109,1.07156,4.76624,1.45151,4.356199999999999,1.51017C3.91965,1.57261,3.57261,1.91965,3.51017,2.3562C3.45151,2.76624,3.07156,3.05109,2.66152,2.99244C2.25148,2.93379,1.96663,2.55384,2.02528,2.1438C2.18209,1.0475,3.0475000000000003,0.182092,4.143800000000001,0.025279C4.55384,-0.0333726,4.93379,0.251484,4.99244,0.661524ZM2.75,4C3.1642099999999997,4,3.5,4.33579,3.5,4.75L3.5,6.25C3.5,6.66421,3.1642099999999997,7,2.75,7C2.33579,7,2,6.66421,2,6.25L2,4.75C2,4.33579,2.33579,4,2.75,4ZM13.25,4C13.6642,4,14,4.33579,14,4.75L14,6.25C14,6.66421,13.6642,7,13.25,7C12.8358,7,12.5,6.66421,12.5,6.25L12.5,4.75C12.5,4.33579,12.8358,4,13.25,4ZM2.75,8C3.1642099999999997,8,3.5,8.33579,3.5,8.75L3.5,9.01772C3.58165,9.00604,3.66512,9,3.75,9L4.25,9C4.664210000000001,9,5,9.33579,5,9.75C5,10.1642,4.664210000000001,10.5,4.25,10.5L3.75,10.5C3.61193,10.5,3.5,10.6119,3.5,10.75L3.5,11.5C3.5,11.78,3.61407,12.0319,3.80018,12.2143C4.09603,12.5042,4.10084,12.9791,3.81094,13.2749C3.52103,13.5708,3.04618,13.5756,2.75033,13.2857C2.28817,12.8328,2,12.1994,2,11.5L2,10.75L2,8.75C2,8.33579,2.33579,8,2.75,8ZM13.25,8C13.6642,8,14,8.33579,14,8.75L14,9.75L14,13.25C14,13.6642,13.6642,14,13.25,14L10.75,14C10.3358,14,10,13.6642,10,13.25C10,12.8358,10.3358,12.5,10.75,12.5L12.5,12.5L12.5,10.5L11.75,10.5C11.3358,10.5,11,10.1642,11,9.75C11,9.33579,11.3358,9,11.75,9L12.5,9L12.5,8.75C12.5,8.33579,12.8358,8,13.25,8ZM6,9.75C6,9.33579,6.33579,9,6.75,9L9.25,9C9.66421,9,10,9.33579,10,9.75C10,10.1642,9.66421,10.5,9.25,10.5L6.75,10.5C6.33579,10.5,6,10.1642,6,9.75ZM5,12.25L5,15.5C5,15.706,5.235189999999999,15.8236,5.4,15.7L6.85,14.6125C6.93889,14.5458,7.06111,14.5458,7.15,14.6125L8.6,15.7C8.76481,15.8236,9,15.706,9,15.5L9,12.25C9,12.1119,8.888069999999999,12,8.75,12L5.25,12C5.11193,12,5,12.1119,5,12.25Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
