/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { RobotWorkGroup, RobotWorkInstanceRelationReq, RobotWorkInstanceRelationRes } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface FindGroupsParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
  /** scope */
  scope:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /** robotWorkType */
  robotWorkType: 'CHECK_ITEM' | 'FLOW_PROCESS' | 'INSIGHT';
  /** robotGenericFlag */
  robotGenericFlag: 'NORMAL' | 'TEMPLATE';
}
/**
 * No description
 * @tags RobotWork
 * @name FindGroups
 * @summary 查询可使用的robot
 * @request POST:/api/v1/robot/findGroups
 */
export async function findGroups(query: FindGroupsParams, options?: MethodOptions): Promise<RobotWorkGroup[]> {
  return request(`${baseUrl}/api/v1/robot/findGroups`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface InstantiateGroupsParams {
  /** scope */
  scope:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /** robotWorkType */
  robotWorkType: 'CHECK_ITEM' | 'FLOW_PROCESS' | 'INSIGHT';
  /**
   * entityId
   * @format int64
   */
  entityId: number;
}
/**
 * No description
 * @tags RobotWork
 * @name InstantiateGroups
 * @summary 实例化robot模板并启用
 * @request POST:/api/v1/robot/instantiateGroups
 */
export async function instantiateGroups(
  query: InstantiateGroupsParams,
  data: number[],
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/robot/instantiateGroups`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface QueryRelationsParams {
  /**
   * entityId
   * @format int64
   */
  entityId: number;
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /** robotWorkType */
  robotWorkType: 'CHECK_ITEM' | 'FLOW_PROCESS' | 'INSIGHT';
  /**
   * templateId
   * @format int64
   */
  templateId?: number;
}
/**
 * No description
 * @tags RobotWork
 * @name QueryRelations
 * @summary 查询现有关联关系
 * @request POST:/api/v1/robot/queryRelations
 */
export async function queryRelations(
  query: QueryRelationsParams,
  options?: MethodOptions,
): Promise<RobotWorkInstanceRelationRes[]> {
  return request(`${baseUrl}/api/v1/robot/queryRelations`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags RobotWork
 * @name AddRelation
 * @summary addRelation
 * @request POST:/api/v1/robot/relation
 */
export async function addRelation(data: RobotWorkInstanceRelationReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/robot/relation`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags RobotWork
 * @name DeleteRelation
 * @summary deleteRelation
 * @request DELETE:/api/v1/robot/relation/{id}
 */
export async function deleteRelation(id: number, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/robot/relation/${id}`, {
    method: 'DELETE',
    ...options,
  });
}
