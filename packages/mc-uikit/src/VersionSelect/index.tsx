import React, { useEffect, useRef } from 'react';
import { debounce } from 'lodash-es';

import { useRequest } from '@ali/mc-request';
import { queryReleasePage, QueryReleasePageParams } from '@ali/mc-services/ReleaseSheet';

import { default as PrefixSelect, PrefixSelectProps } from '../PrefixSelect';
import { PaginationResult, ReleaseBO } from '@ali/mc-services';

export type VersionSelectProps = PrefixSelectProps & {
  /**
   * 应用、模块 ID
   */
  applicationId?: number;

  /**
   * 集成区 ID
   */
  integrateAreaId?: number;

  /**
   * 变更单 ID
   */
  alterSheetId?: number;

  /**
   * 发布类型
   */
  releaseType?: QueryReleasePageParams['releaseType'];

  /**
   * 发布类型
   */
  publishType?: string;

  /**
   * 发布单来源
   */
  originDetailList: string;

  /**
   * 发布状态
   */
  status?: QueryReleasePageParams['statusList'];
};

export default function VersionSelect(props: VersionSelectProps) {
  const {
    applicationId,
    integrateAreaId,
    alterSheetId,
    releaseType = 'NORMAL',
    publishType,
    originDetailList,
    status: statusList,
    value,
    ...rest
  } = props;

  const pagination = useRef<{ pageNum: number; pageSize: number }>({
    pageNum: 1,
    pageSize: 10,
  });
  const {
    data,
    run: doRequest,
    loading,
  } = useRequest<PaginationResult<ReleaseBO>, [QueryReleasePageParams]>(queryReleasePage, {
    onSuccess: (resData) => {
      if (resData?.items?.length && !value) {
        props.onChange && props.onChange(resData.items[0].id, {
          label: resData.items[0].version,
          value: resData.items[0].id,
          data: resData.items[0],
        });
      }
    },
  });

  useEffect(() => {
    if (applicationId && data?.items?.[0]?.applicationId !== applicationId) {
      doRequest({
        applicationId,
        integrateAreaId,
        alterSheetId,
        releaseType,
        publishType,
        statusList,
        originDetailList,
        pageNum: pagination.current.pageNum,
        pageSize: pagination.current.pageSize,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applicationId, integrateAreaId, alterSheetId, releaseType, publishType, statusList]);

  // useEffect(() => {
  //   if (needInitValue) {
  //     const { onChange = () => {} } = props;
  //     const obj = data?.items[0];
  //     onChange(obj?.version, {
  //       label: obj?.version,
  //       value: obj?.version,
  //       data: obj,
  //     });
  //   }
  // }, [data, needInitValue]);

  const onSearch = (keyword?: string | undefined) => {
    doRequest({
      applicationId,
      integrateAreaId,
      alterSheetId,
      releaseType,
      publishType,
      originDetailList,
      keyWord: keyword || undefined,
      statusList,
    });
  };

  const options = data?.items?.map((item) => ({
    label: item.version,
    value: item.id,
    data: item,
  }));

  return (
    <PrefixSelect
      placeholder="输入关键字搜索版本号"
      {...rest}
      value={value || options?.[0]?.value}
      showSearch
      onClear={onSearch}
      filterOption={false}
      onSearch={debounce(onSearch, 500)}
      loading={loading}
      options={options || []}
    />
  );
}
