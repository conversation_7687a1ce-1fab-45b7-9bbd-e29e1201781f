import * as React from 'react';
import type { SVGProps } from 'react';
const SvgOverview = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    viewBox="0 0 16 16"
    className="anticon"
    focusable="false"
    width="1em"
    height="1em"
    aria-hidden="true"
    {...props}
  >
    <defs>
      <clipPath id="master_svg0_4723_78575">
        <path d="M0 0H16V16H0z" />
      </clipPath>
      <clipPath id="master_svg1_4723_78576">
        <path d="M0 0H16V16H0z" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_4723_78575)">
      <g clipPath="url(#master_svg1_4723_78576)">
        <path
          d="M15.75,6.257568359375C16.7165,6.257568359375,17.5,5.474068359375,17.5,4.507568359375L17.5,2.007568359375C17.5,1.041068359375,16.7165,0.257568359375,15.75,0.257568359375L13.25,0.257568359375C12.2835,0.257568359375,11.5,1.041068359375,11.5,2.007568359375L11.5,4.507568359375C11.5,5.474068359375,12.2835,6.257568359375,13.25,6.257568359375L15.75,6.257568359375ZM16,4.507568359375C16,4.645638359375,15.8881,4.757568359375,15.75,4.757568359375L13.25,4.757568359375C13.1119,4.757568359375,13,4.645638359375,13,4.507568359375L13,2.007568359375C13,1.869498359375,13.1119,1.757568359375,13.25,1.757568359375L15.75,1.757568359375C15.8881,1.757568359375,16,1.869498359375,16,2.007568359375L16,4.507568359375Z"
          fillRule="evenodd"
          fill="currentColor"
          fillOpacity={1}
          transform="matrix(0.7071067690849304,0.7071067690849304,-0.7071067690849304,0.7071067690849304,3.5504004859394627,-8.056287815517862)"
        />
        <path
          d="M12.75,14.5C13.7165,14.5,14.5,13.7165,14.5,12.75L14.5,10.25C14.5,9.2835,13.7165,8.5,12.75,8.5L10.25,8.5C9.2835,8.5,8.5,9.2835,8.5,10.25L8.5,12.75C8.5,13.7165,9.2835,14.5,10.25,14.5L12.75,14.5ZM13,12.75C13,12.8881,12.8881,13,12.75,13L10.25,13C10.1119,13,10,12.8881,10,12.75L10,10.25C10,10.1119,10.1119,10,10.25,10L12.75,10C12.8881,10,13,10.1119,13,10.25L13,12.75Z"
          fillRule="evenodd"
          fill="currentColor"
          fillOpacity={1}
        />
        <path
          d="M7.5,12.75C7.5,13.7165,6.7165,14.5,5.75,14.5L3.25,14.5C2.2835,14.5,1.5,13.7165,1.5,12.75L1.5,10.25C1.5,9.2835,2.2835,8.5,3.25,8.5L5.75,8.5C6.7165,8.5,7.5,9.2835,7.5,10.25L7.5,12.75ZM5.75,13C5.88807,13,6,12.8881,6,12.75L6,10.25C6,10.1119,5.88807,10,5.75,10L3.25,10C3.11193,10,3,10.1119,3,10.25L3,12.75C3,12.8881,3.11193,13,3.25,13L5.75,13Z"
          fillRule="evenodd"
          fill="currentColor"
          fillOpacity={1}
        />
        <path
          d="M5.75,7.5C6.7165,7.5,7.5,6.7165,7.5,5.75L7.5,3.25C7.5,2.2835,6.7165,1.5,5.75,1.5L3.25,1.5C2.2835,1.5,1.5,2.2835,1.5,3.25L1.5,5.75C1.5,6.7165,2.2835,7.5,3.25,7.5L5.75,7.5ZM6,5.75C6,5.88807,5.88807,6,5.75,6L3.25,6C3.11193,6,3,5.88807,3,5.75L3,3.25C3,3.11193,3.11193,3,3.25,3L5.75,3C5.88807,3,6,3.11193,6,3.25L6,5.75Z"
          fillRule="evenodd"
          fill="currentColor"
          fillOpacity={1}
        />
      </g>
    </g>
  </svg>
);
export default SvgOverview;
