import { Button } from 'antd';
import React, { useState } from 'react';
import ModalContent from './ModalContent';
import { CreateDynamicPublishModalProps } from './type';

const CreateDynamicPublishModal = (props: CreateDynamicPublishModalProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const handleOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
      setOpen(false);
  };
  return (<>
    <Button
      type="primary"
      onClick={handleOpen}
    >
      创建动态化正式发布单
    </Button>
    <ModalContent
      open={open}
      onCancel={handleClose}
      {...props}
    />
  </>);
};
 export default CreateDynamicPublishModal;