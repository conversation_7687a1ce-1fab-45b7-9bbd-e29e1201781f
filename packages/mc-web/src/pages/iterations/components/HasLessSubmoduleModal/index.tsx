import React from 'react';
import styles from './index.module.less';
import { Flex, Card, Modal, theme, Typography } from 'antd';
import { MonorepoIntegrationCheckResult } from '@ali/mc-services';
import { Link } from 'ice';
const { Text } = Typography;

interface HasLessSubmoduleModalProps {
  data?: MonorepoIntegrationCheckResult & { moduleName?: string; moduleId?: number };
  modalOpen?: boolean;
  closeModal?: (isRefresh?: boolean) => void;
}

const HasLessSubmoduleModal = (props: HasLessSubmoduleModalProps) => {
  const { data, modalOpen, closeModal } = props;

  const { moduleName, currentSubmoduleCount, deletedSubmodules, integratedVersion, integratedSubmoduleCount, moduleId } = data ?? {};
  const { token } = theme.useToken();

  return (
    <Modal
      title="差异详情"
      open={modalOpen}
      okText="关闭"
      cancelButtonProps={{ style: { display: 'none' } }}
      onOk={() => {
        closeModal && closeModal(false);
      }}
      onCancel={() => {
        closeModal && closeModal(false);
      }}
      destroyOnClose
      className={styles.hasLessSubmoduleModal}
    >
      <Flex vertical>
        <Flex vertical gap={token.marginXS}>
          <Flex className={styles.title}>概况</Flex>
          <Card className={styles.mainInfoCard}>
            <Flex vertical gap={token.marginXS}>
              <Flex align="center">
                <Flex className={styles.itemLabel}>主模块：</Flex>
                <Link
                  className={styles.mainModuleLink}
                  to={`/modules/${moduleId}/setting`}
                  target={`module_${moduleId}`}
                >
                  {moduleName || '-'}
                </Link>
              </Flex>
              <Flex align="center">
                <Flex className={styles.itemLabel}>当前版本包含子模块：</Flex>
                <span>{currentSubmoduleCount || '-'}</span>
              </Flex>
              <Flex align="center">
                <Flex className={styles.itemLabel}>集成区版本包含子模块：</Flex>
                <span>{integratedSubmoduleCount || '-'}</span>
                <span className={styles.itemLabel}>
                  （版本号：
                  <span className={styles.primaryText}>{integratedVersion}</span>
                  ）</span>
              </Flex>
            </Flex>
          </Card>
        </Flex>
        <Flex vertical gap={token.marginXS}>
          <Flex className={styles.title} gap={token.marginXS}>
            缺失子模块
            <span className={styles.primaryText}>{deletedSubmodules?.length || 0}</span>
            个</Flex>
          <Card className={styles.mainInfoCard}>
            <Flex gap={token.margin} vertical>
              {deletedSubmodules?.map(item => {
                return (<Flex className={styles.submoduleItem} key={item?.id} vertical gap={token.marginSM}>
                  <Text style={{ cursor: 'pointer' }} onClick={() => { window.open(`/#/app/${item?.id}/detail/overview`, '_blank', 'noopener'); }} strong ellipsis={{ tooltip: item?.name }}>
                    <Link to={''}>{item?.name}</Link>
                  </Text>
                  <Flex>
                    <span style={{ color: token.colorTextLabel, whiteSpace: 'nowrap' }}>唯一标志：</span>
                    <Text ellipsis={{ tooltip: item?.identifier }}>{item?.identifier}</Text>
                  </Flex>
                </Flex>);
              },
              )}
            </Flex>
          </Card>
        </Flex>
      </Flex>
    </Modal>
  );
};

export default HasLessSubmoduleModal;
